<!DOCTYPE html>
<html>

<head>
    <title>API Test</title>
</head>

<body>
    <h1>API Connectivity Test</h1>
    <button onclick="testHealth()">Test Health Endpoint</button>
    <button onclick="testFileUpload()">Test File Upload</button>
    <div id="result"></div>

    <script>
        async function testHealth() {
            try {
                const response = await fetch('http://localhost:5000/health');
                const data = await response.json();
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }

        async function testFileUpload() {
            try {
                // Create a simple text file
                const fileContent = 'pH: 6.8\nNitrogen: 145 kg/ha\nPhosphorus: 28 kg/ha';
                const file = new File([fileContent], 'test.txt', { type: 'text/plain' });

                const formData = new FormData();
                formData.append('file', file);

                const response = await fetch('http://localhost:5000/api/soil-analysis/test-ocr', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }
    </script>
</body>

</html>