<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            color: #4CAF50;
            font-size: 18px;
            margin-bottom: 15px;
        }
        .info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌱 Agri-Lift Soil Insight - Test Page</h1>
        
        <div class="success">✅ Basic HTML is working!</div>
        
        <div class="info">
            <strong>This is a simple HTML test page to verify:</strong><br>
            • Browser can load HTML files<br>
            • CSS styling works<br>
            • JavaScript execution works<br>
        </div>
        
        <button onclick="testJavaScript()">Test JavaScript</button>
        
        <div id="js-result" style="margin-top: 15px;"></div>
        
        <div class="info" style="margin-top: 20px;">
            <strong>Next Steps:</strong><br>
            1. If you see this page, HTML/CSS works<br>
            2. Click the button to test JavaScript<br>
            3. If JavaScript works, the issue is with React/Vite<br>
            4. If JavaScript doesn't work, there's a browser issue<br>
        </div>
    </div>

    <script>
        function testJavaScript() {
            const resultDiv = document.getElementById('js-result');
            resultDiv.innerHTML = '<div style="color: #4CAF50; font-weight: bold;">✅ JavaScript is working!</div>';
            
            // Test fetch API
            fetch('http://localhost:8085/')
                .then(response => {
                    resultDiv.innerHTML += '<div style="color: #2196F3;">✅ Fetch API works - can reach localhost:8085</div>';
                })
                .catch(error => {
                    resultDiv.innerHTML += '<div style="color: #f44336;">❌ Cannot reach localhost:8085: ' + error.message + '</div>';
                });
        }
        
        // Auto-test on page load
        window.onload = function() {
            console.log('Test page loaded successfully');
            document.getElementById('js-result').innerHTML = '<div style="color: #4CAF50;">✅ Page loaded, JavaScript is running</div>';
        };
    </script>
</body>
</html>
