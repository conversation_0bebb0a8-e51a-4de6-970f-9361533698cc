<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Connection</title>
    <script>
        async function testBackendConnection() {
            try {
                const response = await fetch('http://localhost:5000/health');
                const data = await response.json();
                document.getElementById('backend-result').textContent = JSON.stringify(data, null, 2);
                document.getElementById('backend-status').textContent = 'Connected';
                document.getElementById('backend-status').style.color = 'green';
            } catch (error) {
                document.getElementById('backend-result').textContent = error.message;
                document.getElementById('backend-status').textContent = 'Failed';
                document.getElementById('backend-status').style.color = 'red';
            }
        }

        async function testFrontendConnection() {
            try {
                const response = await fetch('http://localhost:8080');
                const text = await response.text();
                document.getElementById('frontend-status').textContent = 'Connected';
                document.getElementById('frontend-status').style.color = 'green';
                // Check if the response contains the root div
                if (text.includes('<div id="root"></div>')) {
                    document.getElementById('frontend-result').textContent = 'Frontend is accessible, but the React app might not be loading correctly.';
                } else {
                    document.getElementById('frontend-result').textContent = 'Frontend is accessible.';
                }
            } catch (error) {
                document.getElementById('frontend-result').textContent = error.message;
                document.getElementById('frontend-status').textContent = 'Failed';
                document.getElementById('frontend-status').style.color = 'red';
            }
        }

        window.onload = function() {
            testBackendConnection();
            testFrontendConnection();
        };
    </script>
</head>
<body>
    <h1>Connection Test</h1>
    
    <h2>Backend Status: <span id="backend-status">Checking...</span></h2>
    <pre id="backend-result">Loading...</pre>
    
    <h2>Frontend Status: <span id="frontend-status">Checking...</span></h2>
    <pre id="frontend-result">Loading...</pre>
</body>
</html>